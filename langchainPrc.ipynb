from dotenv import load_dotenv
load_dotenv(".env", override=True)

from langchain.chat_models import init_chat_model
llm = init_chat_model("gemini-2.5-pro", model_provider="google_genai")

from langgraph.graph import MessagesState
from typing import Literal
from langgraph.types import Command

class State(MessagesState):
    # We can add a specific key to our state for the email input
    post_input: dict
    classification_decision: Literal["ignore", "notify", "create task"]

from langchain.tools import tool
from pydantic import BaseModel

@tool
def create_task(project: str) -> str:
    """Create task for a specific project."""
    # Placeholder response - in real app would send email
    return f"Created task for {project} project."


@tool
class Done(BaseModel):
      """E-mail has been sent."""
      done: bool

from pydantic import BaseModel, Field

class ProjectSchema(BaseModel):
    """Analyze the project"""
    
    reasoning: str = Field(
        description="Step-by-step reasoning behind the classification."
    )
    project_name: str = Field(
        description="The project name."
    )
    funding: int = Field(
        description="Function of the projet "
    )
    Rating: int = Field(
        description="Rating of the project"
    )
    project_type: str = Field(
        description="Type of the project"
    )
    project_category : str = Field(
        description="Category of the project"
    )
    

class RouterSchema(BaseModel):
    """ Analyze the post and route it according to its content."""

    reasoning: str = Field(
        description="Step-by-step reasoning behind the classification."
    )
    classification: Literal["ignore", "notify", "create task"] = Field(
        description="The classification of an post: 'ignore' for irrelevant post, "
        "'notify' for important information that doesn't require response, "
        "'create task' for post that requires to create a task",
    )
    
llm_router = llm.with_structured_output(RouterSchema)

from TelegramAi.prompts import triage_system_prompt, default_background, default_triage_instructions , triage_user_promt



def triage_router(state: State) -> Command[Literal["response_agent", "__end__"]]:
    """Analyze post content to decide if we should create a task, notify, or ignore."""
    
    # Parse the post input
    post_thread = state["post_input"]
    system_prompt = triage_system_prompt.format(
        background=default_background,
        triage_instructions=default_triage_instructions
    )
    
    user_prompt = triage_user_promt.format(
        post_thread=post_thread
    )
    
    # Run the router LLM
    result = llm_router.invoke(
        [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt},
        ]
    )
    print(result)
    

model_with_tools = llm.bind_tools([create_task], parallel_tool_calls=False)

output = model_with_tools.invoke("Create a task for the LangChain project to add support for Gemini. That is complete the agent integration. No question")

tool_name = output.tool_calls[0]['name']
tool_name

result = create_task.invoke(args)
result

from typing_extensions import TypedDict
from typing import Annotated
from langgraph.graph.message import add_messages


class State(TypedDict):
    # Messages have the type "list". The `add_messages` function
    # in the annotation defines how this state key should be updated
    # (in this case, it appends messages to the list, rather than overwriting them)
    messages: Annotated[list, add_messages]

from typing import TypedDict
from langgraph.graph import StateGraph, START, END

def call_llm(state: State) -> State:
    """Run LLM"""

    output = model_with_tools.invoke(state["messages"])
    return {"messages": [output]}


def run_tool(state: State):
    """Performs the tool call"""

    result = []
    for tool_call in state["messages"][-1].tool_calls:
        observation = create_task.invoke(tool_call["args"])
        result.append({"role": "tool", "content": observation, "tool_call_id": tool_call["id"]})
    return {"messages": result}

def should_continue(state: State):
    """Route to tool handler, or end if Done tool called"""
    messages = state["messages"]
    last_message = messages[-1]
    if last_message.tool_calls:
        return "run_tool"
    return END

workflow = StateGraph(State)
workflow.add_node("call_llm", call_llm)
workflow.add_node("run_tool", run_tool)
workflow.add_edge(START, "call_llm")
workflow.add_conditional_edges("call_llm", should_continue, {"run_tool": "run_tool", END: END})
workflow.add_edge("run_tool", END)

app = workflow.compile()

config = {"configurable": {"thread_id": "1"}}

result = app.invoke({"messages": [{"role": "user", "content": "Hello"}]})
for m in result['messages']:
    m.pretty_print()

from IPython.display import Image, display

try:
    display(Image(app.get_graph().draw_mermaid_png()))
except Exception:
    # This requires some extra dependencies and is optional
    pass