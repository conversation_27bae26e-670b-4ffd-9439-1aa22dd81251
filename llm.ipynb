import getpass
import os

if not os.environ.get("GOOGLE_API_KEY"):
  os.environ["GOOGLE_API_KEY"] = getpass.getpass("Enter API key for Google Gemini: ")

from langchain.chat_models import init_chat_model

llm = init_chat_model("gemini-2.5-pro", model_provider="google_genai")


if not os.environ.get("GOOGLE_API_KEY"):
  os.environ["GOOGLE_API_KEY"] = getpass.getpass("Enter API key for Google Gemini: ")

from langchain_google_genai import GoogleGenerativeAIEmbeddings

embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")

if not os.environ.get("TAVILY_API_KEY"):
  os.environ["TAVILY_API_KEY"] = getpass.getpass("Enter API key for Tavily: ")

import chromadb

client = chromadb.PersistentClient(path="./chroma_langchain_db")

from langchain_chroma import Chroma

vector_store = Chroma(
    client=client,
    collection_name="example_collection",
    embedding_function=embeddings,
    # persist_directory="./chroma_langchain_db",  # Where to save data locally, remove if not necessary ( in upper I decler the client so we need this)
)


from typing_extensions import TypedDict
from typing import Annotated
from langgraph.graph.message import add_messages


class State(TypedDict):
    # Messages have the type "list". The `add_messages` function
    # in the annotation defines how this state key should be updated
    # (in this case, it appends messages to the list, rather than overwriting them)
    messages: Annotated[list, add_messages]

from langchain_tavily import TavilySearch

tool = TavilySearch(max_results=3)
tools = [tool]
tool.invoke("What's a 'node' in LangGraph?")

llm_with_tools = llm.bind_tools(tools)


def ChatBot(state: State):
    aiMessage = llm_with_tools.invoke(state["messages"])
    return {"messages": [aiMessage]}


from langgraph.graph import START, MessagesState, StateGraph, END
from langgraph.prebuilt import ToolNode, tools_condition


workflow = StateGraph(MessagesState)


tool_node = ToolNode(tools=[tool])

workflow.add_node("chatbot", ChatBot)
workflow.add_node("tools", tool_node)

workflow.add_conditional_edges("chatbot", tools_condition)
workflow.add_edge('tools', 'chatbot')

workflow.add_edge(START, "chatbot")
workflow.add_edge("chatbot", END)

from langgraph.checkpoint.memory import InMemorySaver

memory = InMemorySaver()

app = workflow.compile(checkpointer=memory)

config = {"configurable": {"thread_id": "1"}}
def Chat_with_llm(user_input : str):
    output = ""
    for event in app.stream({"messages": [{"role": "user", "content": user_input}]}, config=config, stream_mode="values"):
        # print(event["chatbot"]["messages"][0].content)
        # print(type(event))
        # for value in event.values():
        #     print(value)
        output = (event["messages"][-1].content)
    return output


Chat_with_llm("What you're name")

result = app.invoke({"messages": [{"role": "user", "content": "Is women voice is help to reduece man stress"}]}, config=config)


for m in result["messages"]:
    m.pretty_print()

from IPython.display import Image, display

try:
    display(Image(app.get_graph().draw_mermaid_png()))
except Exception:
    # This requires some extra dependencies and is optional
    pass

config2 = {"configurable": {"thread_id": "2"}}



app.invoke({"messages": [{"role": "user", "content": "what is you're name"}]}, config=config2)



import config

async def get_client():
    client = TelegramClient(
        session=config.SESSION_NAME,
        api_hash=config.API_HASH,
        api_id=config.API_ID,
    )
    await client.start()
    
    if not await client.is_user_authorized():
        await client.send_code_request(config.PHONE_NUMBER)
        code = input('Enter the code: ')
        await client.sign_in(config.PHONE_NUMBER, code)
        
    return client

class TeleBot:
    def __init__(self, tg_client):
        self.tg_client = tg_client
        self._handler = None

    async def rawMessage(self, channel_link: str, message_limit: int):
        try:
            channel_entity = await self.tg_client.get_entity(channel_link)
            messages = self.tg_client.iter_messages(channel_entity, limit=message_limit)
            return messages
        
        except FloodWaitError as e:
            print(f"FloodWaitError: Wait {e.seconds} seconds.")
            await asyncio.sleep(e.seconds)
            
        except Exception as e:
            print(f"Error getting channel messages: {e}")
            return None


from langgraph.graph import START, MessagesState, StateGraph
from langgraph.checkpoint.memory import MemorySaver

stat_builder = StateGraph(MessagesState)

# Index chunks
_ = vector_store.add_documents(documents=all_splits)

# Define prompt for question-answering
# N.B. for non-US LangSmith endpoints, you may need to specify
# api_url="https://api.smith.langchain.com" in hub.pull.
prompt = hub.pull("rlm/rag-prompt")


# Define state for application
class State(TypedDict):
    question: str
    context: List[Document]
    answer: str

# Define application steps
from langchain_core.tools import tool
from langgraph.graph import MessagesState, StateGraph


@tool(response_format="content_and_artifact")
def retrieve(query: str):
    """Retrieve information related to a query."""
    retrieved_docs = vector_store.similarity_search(query, k=2)
    serialized = "\n\n".join(
        (f"Source: {doc.metadata}\nContent: {doc.page_content}")
        for doc in retrieved_docs
    )
    return serialized, retrieved_docs



def generate(state: State):
    docs_content = "\n\n".join(doc.page_content for doc in state["context"])
    messages = prompt.invoke({"question": state["question"], "context": docs_content})
    response = llm.invoke(messages)
    return {"answer": response.content}




from langchain_core.messages import SystemMessage
from langgraph.prebuilt import ToolNode

# Step 1: Generate an AIMessage that may include a tool-call to be sent.
def query_or_respond(state: MessagesState):
    """Generate tool call for retrieval or respond."""
    llm_with_tools = llm.bind_tools([retrieve])
    response = llm_with_tools.invoke(state["messages"])
    # MessagesState appends messages to state instead of overwriting
    return {"messages": [response]}


# Step 2: Execute the retrieval.
tools = ToolNode([retrieve])


# Step 3: Generate a response using the retrieved content.
def generate(state: MessagesState):
    """Generate answer."""
    # Get generated ToolMessages
    recent_tool_messages = []
    for message in reversed(state["messages"]):
        if message.type == "tool":
            recent_tool_messages.append(message)
        else:
            break
    tool_messages = recent_tool_messages[::-1]

    # Format into prompt
    docs_content = "\n\n".join(doc.content for doc in tool_messages)
    system_message_content = (
        "You are an assistant for question-answering tasks. "
        "Use the following pieces of retrieved context to answer "
        "the question. If you don't know the answer, say that you "
        "don't know. Use three sentences maximum and keep the "
        "answer concise."
        "\n\n"
        f"{docs_content}"
    )
    conversation_messages = [
        message
        for message in state["messages"]
        if message.type in ("human", "system")
        or (message.type == "ai" and not message.tool_calls)
    ]
    prompt = [SystemMessage(system_message_content)] + conversation_messages

    # Run
    response = llm.invoke(prompt)
    return {"messages": [response]}

# Compile application and test
graph_builder.add_edge(START, "retrieve")
graph = graph_builder.compile()

response = graph.invoke({"question": "What is Task Decomposition?"})
print(response)
print(response["answer"])

document_ids = vector_store.add_documents(documents=all_splits)

print(document_ids[:3])

print(docs[0].page_content)

assert len(docs) == 1
print(f"Total characters: {len(docs[0].page_content)}")

print(docs[0].page_content[:500])