from _typeshed import ReadableBuffer, SupportsRead
from collections.abc import Callable
from pyexpat import errors as errors, model as model
from typing import Any, Final, final
from typing_extensions import CapsuleType, TypeAlias
from xml.parsers.expat import ExpatError as ExpatError

EXPAT_VERSION: Final[str]  # undocumented
version_info: tuple[int, int, int]  # undocumented
native_encoding: str  # undocumented
features: list[tuple[str, int]]  # undocumented

error = ExpatError
XML_PARAM_ENTITY_PARSING_NEVER: Final = 0
XML_PARAM_ENTITY_PARSING_UNLESS_STANDALONE: Final = 1
XML_PARAM_ENTITY_PARSING_ALWAYS: Final = 2

_Model: TypeAlias = tuple[int, int, str | None, tuple[Any, ...]]

@final
class XMLParserType:
    def Parse(self, data: str | ReadableBuffer, isfinal: bool = False, /) -> int: ...
    def ParseFile(self, file: SupportsRead[bytes], /) -> int: ...
    def SetBase(self, base: str, /) -> None: ...
    def GetBase(self) -> str | None: ...
    def GetInputContext(self) -> bytes | None: ...
    def ExternalEntityParserCreate(self, context: str | None, encoding: str = ..., /) -> XMLParserType: ...
    def SetParamEntityParsing(self, flag: int, /) -> int: ...
    def UseForeignDTD(self, flag: bool = True, /) -> None: ...
    def GetReparseDeferralEnabled(self) -> bool: ...
    def SetReparseDeferralEnabled(self, enabled: bool, /) -> None: ...
    @property
    def intern(self) -> dict[str, str]: ...
    buffer_size: int
    buffer_text: bool
    buffer_used: int
    namespace_prefixes: bool  # undocumented
    ordered_attributes: bool
    specified_attributes: bool
    ErrorByteIndex: int
    ErrorCode: int
    ErrorColumnNumber: int
    ErrorLineNumber: int
    CurrentByteIndex: int
    CurrentColumnNumber: int
    CurrentLineNumber: int
    XmlDeclHandler: Callable[[str, str | None, int], Any] | None
    StartDoctypeDeclHandler: Callable[[str, str | None, str | None, bool], Any] | None
    EndDoctypeDeclHandler: Callable[[], Any] | None
    ElementDeclHandler: Callable[[str, _Model], Any] | None
    AttlistDeclHandler: Callable[[str, str, str, str | None, bool], Any] | None
    StartElementHandler: (
        Callable[[str, dict[str, str]], Any]
        | Callable[[str, list[str]], Any]
        | Callable[[str, dict[str, str], list[str]], Any]
        | None
    )
    EndElementHandler: Callable[[str], Any] | None
    ProcessingInstructionHandler: Callable[[str, str], Any] | None
    CharacterDataHandler: Callable[[str], Any] | None
    UnparsedEntityDeclHandler: Callable[[str, str | None, str, str | None, str], Any] | None
    EntityDeclHandler: Callable[[str, bool, str | None, str | None, str, str | None, str | None], Any] | None
    NotationDeclHandler: Callable[[str, str | None, str, str | None], Any] | None
    StartNamespaceDeclHandler: Callable[[str, str], Any] | None
    EndNamespaceDeclHandler: Callable[[str], Any] | None
    CommentHandler: Callable[[str], Any] | None
    StartCdataSectionHandler: Callable[[], Any] | None
    EndCdataSectionHandler: Callable[[], Any] | None
    DefaultHandler: Callable[[str], Any] | None
    DefaultHandlerExpand: Callable[[str], Any] | None
    NotStandaloneHandler: Callable[[], int] | None
    ExternalEntityRefHandler: Callable[[str, str | None, str | None, str | None], int] | None
    SkippedEntityHandler: Callable[[str, bool], Any] | None

def ErrorString(code: int, /) -> str: ...

# intern is undocumented
def ParserCreate(
    encoding: str | None = None, namespace_separator: str | None = None, intern: dict[str, Any] | None = None
) -> XMLParserType: ...

expat_CAPI: CapsuleType
